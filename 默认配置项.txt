# 基本设置
port: 7890
socks-port: 7891
allow-lan: true
bind-address: "*"
mode: rule
log-level: info
ipv6: false

# TUN 模式配置
tun:
  enable: true
  stack: system # system 或 gvisor，推荐 system
  dns-hijack:
    - "any:53"
  auto-route: true
  auto-detect-interface: true
  # device: # Windows下自动分配，无需指定
  mtu: 1502 # 降低MTU避免分片问题
  strict-route: false # 与现有fake-ip配置兼容
  inet4-route-address:
    - 0.0.0.0/1
    - *********/1
  # inet6-route-address: # 由于配置中ipv6: false，暂时禁用IPv6路由
  #   - "::/1"
  #   - "8000::/1"
  endpoint-independent-nat: false
# 外部控制配置（mihomo party必需）
external-controller: 127.0.0.1:9090 # 允许通过 RESTful API 或 Web 界面控制
external-ui: ui # mihomo party Web界面
# secret: ""    # 可设置访问密钥，留空表示无密码
# 性能优化
unified-delay: true
tcp-concurrent: true

find-process-mode: strict
global-client-fingerprint: chrome

# 连接优化参数
keep-alive-idle: 600
keep-alive-interval: 15
disable-keep-alive: false

# 所有节点
all: &all {type: select, use: [订阅 1]}

# 广告过滤锚点
exclude-ads: &exclude-ads "套餐|购买|续费|到期|广告|流量|官网|订阅|更新|剩余|重置|年付|月付|建议|倍率|试用|临时|过期|失效|无效|测试|unavailable|Time remaining until|Remaining data traffic|Plan expiration|traffic|reset|GB|days|搬瓦工"

# 订阅更新配置
p: &p {type: http, interval: 21600, health-check: {enable: true, url: "http://www.gstatic.com/generate_204", interval: 600, timeout: 5, lazy: true, max-failed-times: 3, exclude-filter: *exclude-ads}, smux: {enabled: true, padding: true, protocol: "smux"}}

# 自建节点配置
proxies:

proxy-providers:

  订阅 1:
    <<: *p
    # url:
    url: "https://ym.yunmeng.in/s/d08f9cb014910a67eca23386da902a44"
    exclude-filter: *exclude-ads

  订阅 2: # 落地节点（通过订阅1中转）
    <<: *p
    # url:
    url: "https://molecular.moyuem.de/sub/60c49077c889d27090f19312eba68d68"
    override:
      dialer-proxy: "✈️ 中转选择"  # 所有订阅2节点都通过"中转选择"组进行中转
      
  订阅 3: # 自建节点订阅（直接连接）
    <<: *p
    # url:
    url: "https://molecular.moyuem.de/sub/60c49077c889d27090f19312eba68d68"
    exclude-filter: *exclude-ads
 
  订阅 4: # 在你没有弄明白规则的写法之前，不要随意删减订阅 1 至订阅 8，哪怕 url 为空  自留地
    <<: *p         #T02
    # url:     
    url: ""

  订阅 5: # 
    <<: *p
    # url:
    url: ""
    
  订阅 6: #
    <<: *p
    url: ""
    # url: ""
    
  订阅 7: #
    <<: *p
    url: ""
    # url: ""
    
  订阅 8: # 
    <<: *p
    # url:
    url: ""

# Profile Enhancement Merge Template for Clash Verge
proxy-groups:
  - name: 🚀 节点选择
    # include-all: true
    exclude-filter: *exclude-ads
    type: select
    proxies:
      - ⚡ 自动选择
      - 🔗 链式代理
      - 🔘 dialer-selector
      - ✈️ 中转选择
      - 🇭🇰 香港自动
      - 🇺🇸 美国自动
      - 🇭🇰 香港节点
      - 🇺🇸 美国节点
      - 🇯🇵 日本节点
      - 🇸🇬 新加坡节点
    icon: "https://raw.githubusercontent.com/Koolson/Qure/master/IconSet/Color/Rocket.png"

  - name: ✈️ 中转选择
    type: select
    use: [订阅 1]
    exclude-filter: *exclude-ads
    icon: "https://raw.githubusercontent.com/Koolson/Qure/master/IconSet/Color/Airport.png"

  - name: 🔘 dialer-selector
    type: url-test
    use: [订阅 3]
    exclude-filter: *exclude-ads
    url: http://www.gstatic.com/generate_204
    interval: 300
    tolerance: 100
    timeout: 5000
    lazy: true
    max-failed-times: 3
    icon: "https://raw.githubusercontent.com/Koolson/Qure/master/IconSet/Color/Speedtest.png"

  - name: ⚡ 自动选择
    type: url-test
    use: [订阅 3, 订阅 1]
    exclude-filter: *exclude-ads
    url: http://www.gstatic.com/generate_204
    interval: 300
    tolerance: 150
    timeout: 5000
    lazy: true
    max-failed-times: 3
    icon: "https://raw.githubusercontent.com/Koolson/Qure/master/IconSet/Color/Auto.png"

  - name: 🔗 链式代理
    use: [订阅 2]
    type: select
    exclude-filter: *exclude-ads
    icon: "https://raw.githubusercontent.com/Koolson/Qure/master/IconSet/Color/Bypass.png"

  - name: 🤖 AI
    exclude-filter: *exclude-ads
    type: select
    proxies:
      - 🔗 链式代理
      - 🚀 节点选择
      - 🇺🇸 美国自动
      - 🇺🇸 美国节点
      - 🇭🇰 香港自动
      - 🇭🇰 香港节点
      - ⚡ 自动选择
    icon: "https://raw.githubusercontent.com/Koolson/Qure/master/IconSet/Color/ChatGPT.png"
    hidden: true

  - name: 🌍 国外网站
    exclude-filter: *exclude-ads
    type: select
    proxies:
      - 🔗 链式代理
      - 🚀 节点选择
      - 🔘 dialer-selector
      - 🇭🇰 香港自动
      - 🇺🇸 美国自动
      - 🇭🇰 香港节点
      - 🇺🇸 美国节点
      - 🇯🇵 日本节点
      - 🇸🇬 新加坡节点
      - ⚡ 自动选择
    icon: "https://raw.githubusercontent.com/Koolson/Qure/master/IconSet/Color/Global.png"
    hidden: true



  - name: 🍎 苹果服务
    exclude-filter: *exclude-ads
    type: select
    proxies:
      - 🚀 节点选择
      - 🎯 Direct
      - 🇭🇰 香港自动
      - 🇺🇸 美国自动
      - 🇭🇰 香港节点
      - 🇺🇸 美国节点
      - 🇯🇵 日本节点
      - 🇸🇬 新加坡节点
      - ⚡ 自动选择
    icon: "https://raw.githubusercontent.com/Koolson/Qure/master/IconSet/Color/Apple.png"
    hidden: true

  - name: 🪟 微软服务
    exclude-filter: *exclude-ads
    type: select
    proxies:
      - 🚀 节点选择
      - 🎯 Direct
      - 🇭🇰 香港自动
      - 🇺🇸 美国自动
      - 🇭🇰 香港节点
      - 🇺🇸 美国节点
      - 🇯🇵 日本节点
      - 🇸🇬 新加坡节点
      - ⚡ 自动选择
    icon: "https://raw.githubusercontent.com/Koolson/Qure/master/IconSet/Color/Microsoft.png"
    hidden: true

  - name: 🔍 谷歌服务
    exclude-filter: *exclude-ads
    type: select
    proxies:
      - 🚀 节点选择
      - 🇭🇰 香港自动
      - 🇺🇸 美国自动
      - 🇭🇰 香港节点
      - 🇺🇸 美国节点
      - 🇯🇵 日本节点
      - 🇸🇬 新加坡节点
      - ⚡ 自动选择
    icon: "https://raw.githubusercontent.com/Koolson/Qure/master/IconSet/Color/Google_Search.png"
    hidden: true

  - name: 📧 电报消息
    exclude-filter: *exclude-ads
    type: select
    proxies:
      - 🚀 节点选择
      - 🇭🇰 香港自动
      - 🇺🇸 美国自动
      - 🇭🇰 香港节点
      - 🇺🇸 美国节点
      - 🇯🇵 日本节点
      - 🇸🇬 新加坡节点
      - ⚡ 自动选择
    icon: "https://raw.githubusercontent.com/Koolson/Qure/master/IconSet/Color/Mail.png"

  - name: 💬 推特消息
    exclude-filter: *exclude-ads
    type: select
    proxies:
      - 🚀 节点选择
      - 🇭🇰 香港自动
      - 🇺🇸 美国自动
      - 🇭🇰 香港节点
      - 🇺🇸 美国节点
      - 🇯🇵 日本节点
      - 🇸🇬 新加坡节点
      - ⚡ 自动选择
    icon: "https://raw.githubusercontent.com/Koolson/Qure/master/IconSet/Color/Twitter.png"
    hidden: true

  - name: 🎮 游戏平台
    exclude-filter: *exclude-ads
    type: select
    proxies:
      - 🚀 节点选择
      - 🇭🇰 香港自动
      - 🇺🇸 美国自动
      - 🇭🇰 香港节点
      - 🇺🇸 美国节点
      - 🇯🇵 日本节点
      - 🇸🇬 新加坡节点
      - ⚡ 自动选择
    icon: "https://raw.githubusercontent.com/Koolson/Qure/master/IconSet/Color/Game.png"
    hidden: true



  - name: 🚫 广告拦截
    type: select
    proxies:
      - REJECT
      - 🎯 Direct
    icon: "https://raw.githubusercontent.com/Koolson/Qure/master/IconSet/Color/Advertising.png"

  - name: 🔀 兜底分流
    # include-all: true
    exclude-filter: *exclude-ads
    type: select
    proxies:
      - 🚀 节点选择
      - 🇭🇰 香港自动
      - 🇺🇸 美国自动
      - 🇭🇰 香港节点
      - 🇺🇸 美国节点
      - 🇯🇵 日本节点
      - 🇸🇬 新加坡节点
      - ⚡ 自动选择
    icon: "https://raw.githubusercontent.com/Koolson/Qure/master/IconSet/Color/Bypass.png"

  - name: 🎯 Direct
    type: select
    proxies:
      - DIRECT
    icon: "https://raw.githubusercontent.com/Koolson/Qure/master/IconSet/Color/Direct.png"

  # 地区分组
  - name: "🇭🇰 香港节点"
    type: select
    include-all: true
    exclude-filter: *exclude-ads
    filter: "(?i)^(?=.*(?:香港|hong ?kong|hk|🇭🇰)).*$"
    icon: "https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/refs/heads/release/icon/color/hk.png"
    hidden: true

  - name: "🇺🇸 美国节点"
    type: select
    include-all: true
    hidden: true
    exclude-filter: *exclude-ads
    filter: "(?i)^(?=.*(?:🇺🇸|美国|US|unitedstates|united ?states|america|usa|洛杉矶|达拉斯|New ?York|西雅图)).*$"
    icon: "https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/refs/heads/release/icon/color/us.png"

  - name: "🇯🇵 日本节点"
    type: select
    include-all: true
    exclude-filter: *exclude-ads
    filter: "(?i)^(?=.*(?:日本|japan|jp|🇯🇵)).*$"
    icon: "https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/refs/heads/release/icon/color/jp.png"
    hidden: true

  - name: "🇸🇬 新加坡节点"
    type: select
    include-all: true
    exclude-filter: *exclude-ads
    filter: "(?i)^(?=.*(?:新加坡|singapore|sg|🇸🇬)).*$"
    icon: "https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/refs/heads/release/icon/color/sg.png"
    hidden: true

  # 智能自动选择组
  - name: "🇭🇰 香港自动"
    type: url-test
    include-all: true
    exclude-filter: *exclude-ads
    filter: "(?i)^(?=.*(?:香港|hong ?kong|hk|🇭🇰)).*$"
    url: http://www.gstatic.com/generate_204
    interval: 600
    tolerance: 100
    timeout: 5000
    lazy: true
    max-failed-times: 3
    icon: "https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/refs/heads/release/icon/color/hk.png"
    hidden: true

  - name: "🇺🇸 美国自动"
    type: url-test
    include-all: true
    hidden: true
    exclude-filter: *exclude-ads
    filter: "(?i)^(?=.*(?:🇺🇸|美国|US|unitedstates|united ?states|america|usa|洛杉矶|达拉斯|New ?York|西雅图)).*$"
    url: http://www.gstatic.com/generate_204
    interval: 600
    tolerance: 100
    timeout: 5000
    lazy: true
    max-failed-times: 3
    icon: "https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/refs/heads/release/icon/color/us.png"

rule-providers:
  # AI 类
  AdBlock:
    type: http
    behavior: classical
    format: yaml
    path: ./ruleset/AdBlock.yaml
    url: "https://cdn.jsdelivr.net/gh/zuluion/Clash-Template-Config@master/Filter/AdBlock.yaml"
    interval: 86400

  BanAD:
    type: http
    behavior: domain
    format: text
    url: "https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/BanAD.list"
    path: ./ruleset/BanAD.list
    interval: 86400

  BanProgramAD:
    type: http
    behavior: domain
    format: text
    url: "https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/BanProgramAD.list"
    path: ./ruleset/BanProgramAD.list
    interval: 86400

  BanEasyList:
    type: http
    behavior: domain
    format: text
    url: "https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/BanEasyList.list"
    path: ./ruleset/BanEasyList.list
    interval: 86400

  reject:
    type: http
    behavior: domain
    format: text
    url: "https://cdn.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/reject.txt"
    path: ./ruleset/reject.txt
    interval: 86400

# 补丁类

  gfw:
    type: http
    behavior: domain
    format: text
    url: "https://cdn.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/gfw.txt"
    path: ./ruleset/gfw.txt
    interval: 86400

  proxy:
    type: http
    behavior: domain
    format: text
    url: "https://cdn.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/proxy.txt"
    path: ./ruleset/proxy.txt
    interval: 86400

  tld-not-cn:
    type: http
    behavior: domain
    format: text
    url: "https://cdn.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/tld-not-cn.txt"
    path: ./ruleset/tld-not-cn.txt
    interval: 86400

  ProxyGFWlist:
    type: http
    behavior: domain
    format: text
    url: "https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/ProxyGFWlist.list"
    path: ./ruleset/ProxyGFWlist.list
    interval: 86400

  ChinaDomain:
    type: http
    behavior: classical
    format: text
    path: ./ruleset/ChinaDomain.list
    url: "https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/ChinaDomain.list"
    interval: 86400

  ChinaCompanyIp:
    type: http
    behavior: classical
    format: text
    path: ./ruleset/ChinaCompanyIp.list
    url: "https://raw.githubusercontent.com/ACL4SSR/ACL4SSR/master/Clash/ChinaCompanyIp.list"
    interval: 86400

  China:
    type: http
    behavior: classical
    format: yaml
    path: ./ruleset/China.yaml
    url: "https://cdn.jsdelivr.net/gh/zuluion/Clash-Template-Config@master/Filter/China.yaml"
    interval: 86400

  cncidr:
    type: http
    behavior: ipcidr
    format: text
    url: "https://cdn.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/cncidr.txt"
    path: ./ruleset/cncidr.txt
    interval: 86400

  lancidr:
    type: http
    behavior: ipcidr
    format: text
    url: "https://cdn.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/lancidr.txt"
    path: ./ruleset/lancidr.txt
    interval: 86400

# 服务类
  telegramcidr:
    type: http
    behavior: ipcidr
    format: text
    url: "https://cdn.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/telegramcidr.txt"
    path: ./ruleset/telegramcidr.txt
    interval: 86400

  PayPal:
    type: http
    behavior: classical
    format: yaml
    path: ./ruleset/PayPal.yaml
    url: "https://cdn.jsdelivr.net/gh/zuluion/Clash-Template-Config@master/Filter/PayPal.yaml"
    interval: 86400

# 游戏类（已由Game规则集覆盖）
  Apple:
    type: http
    behavior: domain
    format: yaml
    url: "https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Clash/Apple/Apple.yaml"
    path: ./ruleset/BlackMatrix7/Apple.yaml
    interval: 86400

  Google:
    type: http
    behavior: classical
    format: text
    url: "https://github.com/Repcz/Tool/raw/X/Clash/Rules/Google.list"
    path: ./ruleset/Google.list
    interval: 86400

  Telegram:
    type: http
    behavior: domain
    format: yaml
    url: "https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Clash/Telegram/Telegram.yaml"
    path: ./ruleset/BlackMatrix7/Telegram.yaml
    interval: 86400

  Twitter:
    type: http
    behavior: classical
    format: text
    url: "https://github.com/Repcz/Tool/raw/X/Clash/Rules/Twitter.list"
    path: ./ruleset/Twitter.list
    interval: 86400

  AI:
    type: http
    behavior: classical
    format: text
    url: "https://github.com/dahaha-365/YaNet/raw/refs/heads/dist/rulesets/mihomo/ai.list"
    path: ./ruleset/YaNet_AI.list
    interval: 86400

  # 新增现代化规则集
  Applications:
    type: http
    behavior: classical
    format: text
    url: "https://cdn.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/applications.txt"
    path: ./ruleset/Loyalsoldier/applications.txt
    interval: 86400

  Download:
    type: http
    behavior: domain
    format: yaml
    url: "https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Clash/Download/Download.yaml"
    path: ./ruleset/BlackMatrix7/Download.yaml
    interval: 86400

  Game:
    type: http
    behavior: domain
    format: yaml
    url: "https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Clash/Game/Game.yaml"
    path: ./ruleset/BlackMatrix7/Game.yaml
    interval: 86400

  Privacy:
    type: http
    behavior: domain
    format: text
    url: "https://cdn.jsdelivr.net/gh/blackmatrix7/ios_rule_script@master/rule/Clash/Privacy/Privacy_Domain.txt"
    path: ./ruleset/Privacy_Domain.txt
    interval: 86400

  Github:
    type: http
    behavior: classical
    format: text
    url: "https://github.com/Repcz/Tool/raw/X/Clash/Rules/Github.list"
    path: ./ruleset/Github.list
    interval: 86400

  Microsoft:
    type: http
    behavior: domain
    format: yaml
    url: "https://raw.githubusercontent.com/blackmatrix7/ios_rule_script/master/rule/Clash/Microsoft/Microsoft.yaml"
    path: ./ruleset/BlackMatrix7/Microsoft.yaml
    interval: 86400

  Lan:
    type: http
    behavior: classical
    format: text
    url: "https://github.com/Repcz/Tool/raw/X/Clash/Rules/Lan.list"
    path: ./ruleset/Lan.list
    interval: 86400

# DNS配置
dns:
  enable: true
  ipv6: false
  listen: '0.0.0.0:1053'
  enhanced-mode: fake-ip
  fake-ip-range: **********/16
  use-hosts: true
  use-system-hosts: false
  prefer-h3: true
  respect-rules: true
  cache-algorithm: arc
  cache-size: 4096

  fake-ip-filter-mode: blacklist
  fake-ip-filter:
    - "geosite:connectivity-check"
    - "geosite:private"
    - '*.lan'
    - localhost.ptlogin2.qq.com
    - '+.srv.nintendo.net'
    - '+.stun.playstation.net'
    - '+.msftconnecttest.com'
    - '+.msftncsi.com'
    - '+.xboxlive.com'
    - 'xbox.*.microsoft.com'
    - '*.battlenet.com.cn'
    - '*.battlenet.com'
    - '*.blzstatic.cn'
    - '*.battle.net'

  default-nameserver:
    - *******
    - *******
    - ************
    - *********
    - *********
    - ************

  direct-nameserver:
    - system
    - https://doh.pub/dns-query
    - https://dns.alidns.com/dns-query
    - ***************
    - ***************
    - ***********
    - ************
    - ************
    - *************
    - **************
  nameserver:
    - tls://*******:853
    - tls://*******:853
    - quic://dns.adguard-dns.com
  proxy-server-nameserver:
    - https://*******/dns-query
    - https://*******/dns-query
    - https://doh.pub/dns-query
    - https://dns.alidns.com/dns-query
    - https://*******/dns-query
    - https://**************/dns-query

  nameserver-policy:
    "geosite:cn,private":
      - https://*********/dns-query
      - https://doh.pub/dns-query
    "geo:cn":
      - https://*********/dns-query

  fallback:
    - https://cloudflare-dns.com/dns-query
    - https://dns.google/dns-query
    - https://*******/dns-query
    - https://*******/dns-query



profile:
  store-selected: true
  store-fake-ip: true

# 流量嗅探配置
sniffer:
  enable: true
  parse-pure-ip: true        # 解析纯IP连接
  force-dns-mapping: true    # 强制DNS映射
  sniff:
    HTTP:
      ports: [80, 8080-8880]
      override-destination: true
    TLS:
      ports: [443, 8443]
  force-domain:
    - "+.v2ex.com"
  skip-domain:
    - "+.baidu.com"
    - "+.bilibili.com"

# GeoData 配置
geodata-mode: true
geo-auto-update: true
geo-update-interval: 24
geodata-loader: memconservative

# ETag支持（官方推荐）
etag-support: true

# NTP时间同步（适合链式代理架构）
ntp:
  enable: true
  server: time.apple.com
  port: 123
  interval: 30

geox-url:
  geoip: "https://github.com/MetaCubeX/meta-rules-dat/releases/download/latest/geoip.dat"
  geosite: "https://github.com/MetaCubeX/meta-rules-dat/releases/download/latest/geosite.dat"
  mmdb: "https://github.com/MetaCubeX/meta-rules-dat/releases/download/latest/geoip.metadb"
  asn: https://github.com/xishang0128/geoip/releases/download/latest/GeoLite2-ASN.mmdb

rules:
  # ===== 最高优先级：广告拦截和安全 =====
  # - DOMAIN,app-updates.chatwise.app,REJECT
  - DOMAIN-SUFFIX,nektony.com,REJECT

  # ===== 中国直连域名 =====
  - DOMAIN-SUFFIX,linux.do,DIRECT

  # ===== 中高优先级：AI服务（使用频率高） =====
  # 🤖 主流AI平台（精确匹配优先）
  - DOMAIN,meta.ai,🤖 AI
  - DOMAIN,claude.ai,🤖 AI
  - DOMAIN,groq.com,🤖 AI
  - DOMAIN,grok.com,🤖 AI
  - DOMAIN,anthropic.com,🤖 AI
  - DOMAIN,ai-pro.org,🤖 AI
  - DOMAIN,api-proxy.me,🤖 AI

  # 🤖 AI服务扩展
  - DOMAIN-SUFFIX,v0.dev,🤖 AI
  - DOMAIN-SUFFIX,bolt.new,🤖 AI
  - DOMAIN-SUFFIX,suno.ai,🤖 AI
  - DOMAIN-SUFFIX,chatwise.app,🤖 AI
  - DOMAIN-SUFFIX,augmentcode.com,🤖 AI
  - DOMAIN-SUFFIX,lovart.ai,🤖 AI
  - DOMAIN-SUFFIX,kilocode.ai,🤖 AI

  # 🤖 AI关键词匹配（放在后面避免过度匹配）
  - DOMAIN-KEYWORD,google,🤖 AI
  - DOMAIN-KEYWORD,claude,🤖 AI
  - DOMAIN-KEYWORD,openai,🤖 AI
  - DOMAIN-KEYWORD,copilot,🤖 AI
  - DOMAIN-KEYWORD,marscode,🤖 AI
  - DOMAIN-KEYWORD,learning.google.com,🤖 AI
  ## Cursor
  - DOMAIN-SUFFIX,cursor.sh,🤖 AI
  - DOMAIN-SUFFIX,cursorapi.com,🤖 AI
  - DOMAIN-SUFFIX,cursor.com,🤖 AI
  - DOMAIN-SUFFIX,cursor-cdn.com,🤖 AI
  - DOMAIN-SUFFIX,anysphere-binaries.s3.us-east-1.amazonaws.com,🤖 AI

  # ===== 中优先级：开发工具和服务 =====
  # 🚀 开发工具和云服务平台
  - DOMAIN-SUFFIX,vercel.com,🚀 节点选择
  - DOMAIN-SUFFIX,netlify.com,🚀 节点选择
  - DOMAIN-SUFFIX,railway.app,🚀 节点选择
  - DOMAIN-SUFFIX,render.com,🚀 节点选择
  - DOMAIN-SUFFIX,supabase.com,🚀 节点选择
  - DOMAIN-SUFFIX,planetscale.com,🚀 节点选择
  - DOMAIN-SUFFIX,github.dev,🚀 节点选择
  - DOMAIN-SUFFIX,github.io,🚀 节点选择
  - DOMAIN-SUFFIX,navicat.com,🚀 节点选择

  # 🔧 在线开发环境和工具
  - DOMAIN-SUFFIX,codepen.io,🚀 节点选择
  - DOMAIN-SUFFIX,codesandbox.io,🚀 节点选择
  - DOMAIN-SUFFIX,stackblitz.com,🚀 节点选择
  - DOMAIN-SUFFIX,replit.com,🚀 节点选择
  - DOMAIN-SUFFIX,gitpod.io,🚀 节点选择
  - DOMAIN-SUFFIX,glitch.com,🚀 节点选择

  # 📦 包管理和CDN服务
  - DOMAIN-SUFFIX,npmjs.com,🚀 节点选择
  - DOMAIN-SUFFIX,unpkg.com,🚀 节点选择
  - DOMAIN-SUFFIX,jsdelivr.net,🚀 节点选择
  - DOMAIN-SUFFIX,cdnjs.com,🚀 节点选择

  # 🌍 其他代理服务
  - DOMAIN-SUFFIX,dadada.acaisbest.com,🚀 节点选择
  - DOMAIN,v2rayse.com,🚀 节点选择
  - DOMAIN-KEYWORD,cocopilot.org,🚀 节点选择
  - DOMAIN-SUFFIX,infini.money,🚀 节点选择

  # 🎮 游戏平台
  - DOMAIN-SUFFIX,battle.net,🎮 游戏平台
  - DOMAIN-SUFFIX,battlenet.com,🎮 游戏平台
  - DOMAIN-SUFFIX,ubisoft.com,🎮 游戏平台
  - DOMAIN-SUFFIX,origin.com,🎮 游戏平台
  - DOMAIN-SUFFIX,ea.com,🎮 游戏平台
  - DOMAIN-SUFFIX,riotgames.com,🎮 游戏平台
  - DOMAIN-SUFFIX,leagueoflegends.com,🎮 游戏平台
  - DOMAIN-SUFFIX,steamcommunity.com,🎮 游戏平台
  - DOMAIN-SUFFIX,epicgames.com,🎮 游戏平台

  # 🌍 特定地区服务
  - DOMAIN-SUFFIX,postdirekt.de,🇭🇰 香港自动

  # ===== 详细AI服务域名（补充远程规则集未覆盖的） =====

  # 🤖 AI平台补充
  - DOMAIN-SUFFIX,console.anthropic.com,🤖 AI
  - DOMAIN-SUFFIX,aistudio.google.com,🤖 AI
  - DOMAIN-SUFFIX,ai.com,🤖 AI

  # 🔧 AI基础设施和API
  - DOMAIN-SUFFIX,chatgpt.com,🤖 AI
  - DOMAIN-SUFFIX,oaistatic.com,🤖 AI
  - DOMAIN-SUFFIX,oaiusercontent.com,🤖 AI

  # 📊 AI分析和统计服务
  - DOMAIN,browser-intake-datadoghq.com,🤖 AI
  - DOMAIN,static.cloudflareinsights.com,🤖 AI
  - DOMAIN-SUFFIX,algolia.net,🤖 AI
  - DOMAIN-SUFFIX,api.statsig.com,🤖 AI
  - DOMAIN-SUFFIX,events.statsigapi.net,🤖 AI

  # 🔐 AI认证和安全服务
  - DOMAIN-SUFFIX,auth0.com,🤖 AI
  - DOMAIN-SUFFIX,client-api.arkoselabs.com,🤖 AI
  - DOMAIN-SUFFIX,identrust.com,🤖 AI

  # 💬 AI客服和通讯
  - DOMAIN-SUFFIX,intercom.io,🤖 AI
  - DOMAIN-SUFFIX,intercomcdn.com,🤖 AI
  - DOMAIN-SUFFIX,chatgpt.livekit.cloud,🤖 AI
  - DOMAIN-SUFFIX,host.livekit.cloud,🤖 AI

  # ⚙️ AI功能控制服务
  - DOMAIN-SUFFIX,featuregates.org,🤖 AI
  - DOMAIN-SUFFIX,launchdarkly.com,🤖 AI
  - DOMAIN-SUFFIX,observeit.net,🤖 AI

  # 💰 AI支付和商业服务
  - DOMAIN-SUFFIX,poe.com,🤖 AI
  - DOMAIN-SUFFIX,stripe.com,🤖 AI
  - DOMAIN-SUFFIX,segment.io,🤖 AI
  - DOMAIN-SUFFIX,sentry.io,🤖 AI
  - DOMAIN-SUFFIX,turn.livekit.cloud,🤖 AI

  # 🌐 Google AI生态
  - DOMAIN-SUFFIX,googleapis.com,🤖 AI
  - DOMAIN-SUFFIX,gstatic.com,🤖 AI

  # 🔧 开发者AI工具
  - DOMAIN-SUFFIX,isroots.com,🤖 AI
  - DOMAIN-SUFFIX,xn--ngstr-lra8j.com,🤖 AI



  # ===== 开发者AI服务补充 =====

  # 🪟 微软AI生态
  - DOMAIN-SUFFIX,copilot.microsoft.com,🤖 AI
  - DOMAIN-SUFFIX,bing.com,🤖 AI

  # 🔬 AI开发和研究平台
  - DOMAIN-SUFFIX,huggingface.co,🤖 AI
  - DOMAIN-SUFFIX,replicate.com,🤖 AI
  - DOMAIN-SUFFIX,together.ai,🤖 AI
  - DOMAIN-SUFFIX,fireworks.ai,🤖 AI
  - DOMAIN-SUFFIX,cohere.ai,🤖 AI
  - DOMAIN-SUFFIX,anyscale.com,🤖 AI

  # 🎨 AI图像和创意工具
  - DOMAIN-SUFFIX,stability.ai,🤖 AI
  - DOMAIN-SUFFIX,leonardo.ai,🤖 AI
  - DOMAIN-SUFFIX,civitai.com,🤖 AI
  - DOMAIN-SUFFIX,runpod.io,🤖 AI

  # 🌐 AI翻译和语言工具
  - DOMAIN-SUFFIX,deepl.com,🤖 AI

  # ===== 主要规则集引用（按优先级排序） =====

  # 🔍 搜索和基础服务
  - RULE-SET,Google,🔍 谷歌服务

  # 🤖 AI服务规则集
  - RULE-SET,AI,🤖 AI

  # 🎮 游戏和娱乐
  - RULE-SET,Game,🎮 游戏平台

  # 📱 平台服务
  - RULE-SET,Apple,🍎 苹果服务
  - RULE-SET,Telegram,📧 电报消息
  - RULE-SET,Twitter,💬 推特消息

  # 🔧 应用和下载
  - RULE-SET,Download,🚀 节点选择
  - RULE-SET,Applications,🚀 节点选择

  # 🪟 微软服务
  - GEOSITE,onedrive,🪟 微软服务
  - GEOSITE,microsoft,🪟 微软服务
  - RULE-SET,Microsoft,🪟 微软服务
  - RULE-SET,Github,🪟 微软服务

  # 💰 支付服务
  - RULE-SET,PayPal,🚀 节点选择

  # 📧 通讯服务补充
  - RULE-SET,telegramcidr,📧 电报消息

  # ===== 广告拦截规则集 =====
  - RULE-SET,Privacy,🚫 广告拦截
  - RULE-SET,AdBlock,🚫 广告拦截
  - RULE-SET,reject,🚫 广告拦截
  - RULE-SET,BanAD,🚫 广告拦截
  - RULE-SET,BanProgramAD,🚫 广告拦截
  - RULE-SET,BanEasyList,🚫 广告拦截

  # ===== 代理规则集 =====
  - GEOSITE,gfw,🌍 国外网站
  - RULE-SET,gfw,🚀 节点选择
  - RULE-SET,proxy,🚀 节点选择
  - RULE-SET,tld-not-cn,🚀 节点选择
  - RULE-SET,ProxyGFWlist,🚀 节点选择

  # ===== 中国直连规则集 =====
  - RULE-SET,China,DIRECT
  - RULE-SET,ChinaDomain,DIRECT,no-resolve
  - RULE-SET,ChinaCompanyIp,DIRECT,no-resolve
  - RULE-SET,cncidr,DIRECT,no-resolve
  - RULE-SET,lancidr,DIRECT,no-resolve
  - GEOIP,lan,DIRECT

  # ===== 最终兜底规则 =====
  - GEOIP,CN,DIRECT,no-resolve
  - GEOSITE,geolocation-cn,DIRECT
  - GEOSITE,geolocation-!cn,🚀 节点选择
  - MATCH,🔀 兜底分流
